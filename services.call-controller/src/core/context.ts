import { PrismaClient } from '@prisma/client';
import { prisma, testDatabaseConnection } from '@/core/database';
import { getLogger } from '@/core/logger';
import { CallRepository } from '@/repositories/call-repository';
import { CallService } from '@/services/call-service';
import { VoiceRouterService } from '@/services/voice-router-service';
import { EventPublisher } from '@/core/events';

const contextLogger = getLogger('ApplicationContext');

/**
 * Application context for managing shared resources.
 * 
 * This class provides a singleton pattern for managing application-wide resources
 * and follows the same pattern as the Python version.
 */
export class ApplicationContext {
  private static instance: ApplicationContext | null = null;
  private static readonly lock = new Promise<void>((resolve) => resolve());

  private initialized = false;
  private dbClient: PrismaClient | null = null;
  private voiceRouterService: VoiceRouterService | null = null;
  private callService: CallService | null = null;
  private eventPublisher: EventPublisher | null = null;
  private callRepository: CallRepository | null = null;

  private constructor() {
    // Private constructor to enforce singleton pattern
  }

  /**
   * Get the singleton instance of the application context.
   * @returns The singleton instance
   */
  static async getInstance(): Promise<ApplicationContext> {
    if (ApplicationContext.instance === null) {
      await ApplicationContext.lock;
      if (ApplicationContext.instance === null) {
        ApplicationContext.instance = new ApplicationContext();
      }
    }
    return ApplicationContext.instance;
  }

  /**
   * Check if the application context is initialized.
   * @returns True if initialized, false otherwise
   */
  get isInitialized(): boolean {
    return this.initialized;
  }

  /**
   * Initialize the application context and its resources.
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      contextLogger.warn('Application context already initialized');
      return;
    }

    contextLogger.info('Initializing application context...');
    
    try {
      // Test database connection first (fail early principle)
      await testDatabaseConnection();

      // Initialize database client
      this.dbClient = prisma;

      // Initialize repositories
      this.callRepository = new CallRepository(this.dbClient);

      // Initialize services
      this.voiceRouterService = new VoiceRouterService();

      // Initialize event publisher
      this.eventPublisher = new EventPublisher();
      await this.eventPublisher.initialize();

      // Initialize call service with dependencies
      this.callService = new CallService(
        this.callRepository,
        this.voiceRouterService,
        this.eventPublisher
      );

      this.initialized = true;
      contextLogger.info('Application context initialized successfully');

    } catch (error) {
      contextLogger.error('Failed to initialize application context:', error);
      this.initialized = false;
      throw error;
    }
  }

  /**
   * Clean up the application context and its resources.
   * This method should be called during application shutdown.
   */
  async cleanup(): Promise<void> {
    contextLogger.info('Cleaning up application context...');

    try {
      // Clean up event publisher
      if (this.eventPublisher) {
        await this.eventPublisher.cleanup();
        this.eventPublisher = null;
      }

      // Close the database connection
      if (this.dbClient) {
        await this.dbClient.$disconnect();
        this.dbClient = null;
      }

      this.initialized = false;
      contextLogger.info('Application context cleanup complete');

    } catch (error) {
      contextLogger.error('Error during application context cleanup:', error);
    }
  }

  /**
   * Reset the singleton instance (mainly for testing).
   * This method should only be used in test scenarios.
   */
  static async resetInstance(): Promise<void> {
    if (ApplicationContext.instance) {
      await ApplicationContext.instance.cleanup();
    }
    ApplicationContext.instance = null;
  }

  // Getters for accessing services (with runtime checks)

  get dbSession(): PrismaClient {
    if (!this.dbClient) {
      throw new Error('Database client not initialized');
    }
    return this.dbClient;
  }

  get voiceRouterServiceInstance(): VoiceRouterService {
    if (!this.voiceRouterService) {
      throw new Error('Voice router service not initialized');
    }
    return this.voiceRouterService;
  }

  get callServiceInstance(): CallService {
    if (!this.callService) {
      throw new Error('Call service not initialized');
    }
    return this.callService;
  }

  get eventPublisherInstance(): EventPublisher {
    if (!this.eventPublisher) {
      throw new Error('Event publisher not initialized');
    }
    return this.eventPublisher;
  }
}

/**
 * Global function to get the application context instance.
 * @returns The application context instance
 */
export async function getAppContext(): Promise<ApplicationContext> {
  return ApplicationContext.getInstance();
}
