import { Request, Response, NextFunction } from 'express';
import { getAppContext } from '@/core/context';
import { getLogger } from '@/core/logger';
// Import the types to ensure they are loaded
import '@/types/express';

const logger = getLogger('ContextMiddleware');

/**
 * Middleware to inject the application context into each request.
 * Similar to FastAPI's Depends() pattern.
 */
export async function injectAppContext(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const appContext = await getAppContext();
    
    if (!appContext.isInitialized) {
      logger.error('Application context not initialized');
      res.status(503).json({
        error: 'Service Unavailable',
        message: 'Application context not initialized',
      });
      return;
    }

    // Inject the context into the request
    req.appContext = appContext;
    next();

  } catch (error) {
    logger.error('Failed to inject application context:', error);
    res.status(500).json({
      error: 'Internal Server Error',
      message: 'Failed to initialize request context',
    });
  }
}
