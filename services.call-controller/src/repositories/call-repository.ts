import { PrismaClient } from '@prisma/client';
import { Call, CreateCallInput, UpdateCallInput, CallStatus } from '@/models/call';

/**
 * Repository class for handling all database operations for the Call model.
 * Provides an abstraction layer over Prisma for call-related database operations.
 */
export class CallRepository {
  constructor(private readonly prisma: PrismaClient) {}

  /**
   * Retrieves a call by its primary key (ID).
   * @param callId - The UUID of the call to retrieve
   * @returns The call if found, null otherwise
   */
  async getById(callId: string): Promise<Call | null> {
    return this.prisma.call.findUnique({
      where: { id: callId },
    });
  }

  /**
   * Retrieves a call by its media session ID.
   * @param mediaSessionId - The media session ID to search for
   * @returns The call if found, null otherwise
   */
  async getByMediaSessionId(mediaSessionId: string): Promise<Call | null> {
    return this.prisma.call.findUnique({
      where: { mediaSessionId },
    });
  }

  /**
   * Creates a new call in the database.
   * @param callData - The call data to create
   * @returns The created call
   */
  async create(callData: CreateCallInput): Promise<Call> {
    return this.prisma.call.create({
      data: {
        ...callData,
        status: CallStatus.QUEUED,
      },
    });
  }

  /**
   * Updates an existing call in the database.
   * @param callId - The ID of the call to update
   * @param updateData - The data to update
   * @returns The updated call
   */
  async update(callId: string, updateData: UpdateCallInput): Promise<Call> {
    return this.prisma.call.update({
      where: { id: callId },
      data: updateData,
    });
  }

  /**
   * Deletes a call from the database.
   * @param callId - The ID of the call to delete
   * @returns The deleted call
   */
  async delete(callId: string): Promise<Call> {
    return this.prisma.call.delete({
      where: { id: callId },
    });
  }

  /**
   * Retrieves all calls with optional filtering.
   * @param status - Optional status filter
   * @returns Array of calls
   */
  async findMany(status?: CallStatus): Promise<Call[]> {
    const whereClause = status ? { status } : {};
    return this.prisma.call.findMany({
      where: whereClause,
      orderBy: { createdAt: 'desc' },
    });
  }

  /**
   * Counts calls by status.
   * @param status - Optional status filter
   * @returns Number of calls
   */
  async count(status?: CallStatus): Promise<number> {
    const whereClause = status ? { status } : {};
    return this.prisma.call.count({
      where: whereClause,
    });
  }
}
