import { ApplicationContext } from '@/core/context';

// Extend Express Request interface to include our application context
declare global {
  namespace Express {
    interface Request {
      appContext: ApplicationContext;
    }
  }
}

// API Error response type
export interface ApiError {
  error: string;
  message?: string;
  statusCode?: number;
}

// API Success response type
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
}

// This export is needed to make this file a module for the global declaration to work
export {};
