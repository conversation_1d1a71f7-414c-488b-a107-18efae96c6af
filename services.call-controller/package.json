{"name": "cortexa-call-controller", "version": "0.1.0", "description": "Cortexa Call Controller Service", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node -r tsconfig-paths/register dist/index.js", "dev": "ts-node -r tsconfig-paths/register src/index.ts", "watch": "tsc --watch", "clean": "rm -rf dist", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "migrate": "prisma migrate dev", "migrate:deploy": "prisma migrate deploy", "db:generate": "prisma generate", "db:push": "prisma db push", "db:studio": "prisma studio"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "uuid": "^9.0.1", "axios": "^1.6.2", "prom-client": "^15.1.0", "winston": "^3.11.0", "kafkajs": "^2.2.4", "@prisma/client": "^5.7.1", "zod": "^3.22.4", "express-async-errors": "^3.1.1", "tsconfig-paths": "^4.2.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/node": "^20.10.5", "@types/uuid": "^9.0.7", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "jest": "^29.7.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.3.3", "prisma": "^5.7.1", "nodemon": "^3.0.2"}, "keywords": ["cortexa", "call-controller", "typescript", "express", "microservice"], "author": "<PERSON> <<EMAIL>>", "license": "UNLICENSED", "engines": {"node": ">=18.0.0"}, "type": "commonjs"}